import * as React from "react";
const MoonIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 66 66"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <foreignObject x="-9.15427" y="-9.15427" width="84.3085" height="84.3085">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        style={{
          backdropFilter: "blur(4.82px)",
          clipPath: "url(#bgblur_0_3662_77383_clip_path)",
          height: "100%",
          width: "100%",
        }}
      ></div>
    </foreignObject>
    <path
      data-figma-bg-blur-radius="9.63279"
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.3075 2.15992C32.7484 2.94236 32.6886 3.91085 32.1548 4.63311C29.1811 8.65617 27.7501 13.6129 28.1222 18.6019C28.4942 23.5908 30.6445 28.2805 34.182 31.818C37.7195 35.3555 42.4092 37.5058 47.3981 37.8778C52.3871 38.2499 57.3438 36.8189 61.3669 33.8452C62.0891 33.3114 63.0576 33.2516 63.8401 33.6925C64.6225 34.1335 65.0729 34.993 64.9903 35.8873C64.4299 41.9515 62.154 47.7307 58.4289 52.5487C54.7038 57.3667 49.6837 61.0241 43.9558 63.093C38.2279 65.162 32.0293 65.5569 26.0852 64.2315C20.1411 62.9061 14.6974 59.9152 10.3911 55.6089C6.08476 51.3026 3.09392 45.8589 1.76853 39.9148C0.443137 33.9707 0.838015 27.7721 2.90696 22.0442C4.9759 16.3163 8.63333 11.2962 13.4513 7.57109C18.2693 3.84601 24.0485 1.57012 30.1127 1.00971C31.007 0.927058 31.8665 1.37747 32.3075 2.15992ZM25.8447 6.395C22.3747 7.33874 19.1081 8.95802 16.2388 11.1764C12.1056 14.372 8.96806 18.6787 7.19319 23.5924C5.41831 28.5062 5.07956 33.8237 6.21657 38.923C7.35358 44.0222 9.91932 48.6922 13.6136 52.3864C17.3078 56.0807 21.9778 58.6464 27.077 59.7834C32.1763 60.9204 37.4938 60.5817 42.4076 58.8068C47.3213 57.0319 51.628 53.8943 54.8236 49.7612C57.042 46.8919 58.6612 43.6253 59.605 40.1553C55.7086 41.9517 51.3939 42.7457 47.0592 42.4225C40.9818 41.9693 35.2689 39.3498 30.9595 35.0405C26.6502 30.7311 24.0307 25.0182 23.5775 18.9408C23.2543 14.6061 24.0483 10.2913 25.8447 6.395Z"
      fill="url(#paint0_linear_3662_77383)"
      stroke="url(#paint1_linear_3662_77383)"
      strokeWidth="1.04377"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <defs>
      <clipPath
        id="bgblur_0_3662_77383_clip_path"
        transform="translate(9.15427 9.15427)"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M32.3075 2.15992C32.7484 2.94236 32.6886 3.91085 32.1548 4.63311C29.1811 8.65617 27.7501 13.6129 28.1222 18.6019C28.4942 23.5908 30.6445 28.2805 34.182 31.818C37.7195 35.3555 42.4092 37.5058 47.3981 37.8778C52.3871 38.2499 57.3438 36.8189 61.3669 33.8452C62.0891 33.3114 63.0576 33.2516 63.8401 33.6925C64.6225 34.1335 65.0729 34.993 64.9903 35.8873C64.4299 41.9515 62.154 47.7307 58.4289 52.5487C54.7038 57.3667 49.6837 61.0241 43.9558 63.093C38.2279 65.162 32.0293 65.5569 26.0852 64.2315C20.1411 62.9061 14.6974 59.9152 10.3911 55.6089C6.08476 51.3026 3.09392 45.8589 1.76853 39.9148C0.443137 33.9707 0.838015 27.7721 2.90696 22.0442C4.9759 16.3163 8.63333 11.2962 13.4513 7.57109C18.2693 3.84601 24.0485 1.57012 30.1127 1.00971C31.007 0.927058 31.8665 1.37747 32.3075 2.15992ZM25.8447 6.395C22.3747 7.33874 19.1081 8.95802 16.2388 11.1764C12.1056 14.372 8.96806 18.6787 7.19319 23.5924C5.41831 28.5062 5.07956 33.8237 6.21657 38.923C7.35358 44.0222 9.91932 48.6922 13.6136 52.3864C17.3078 56.0807 21.9778 58.6464 27.077 59.7834C32.1763 60.9204 37.4938 60.5817 42.4076 58.8068C47.3213 57.0319 51.628 53.8943 54.8236 49.7612C57.042 46.8919 58.6612 43.6253 59.605 40.1553C55.7086 41.9517 51.3939 42.7457 47.0592 42.4225C40.9818 41.9693 35.2689 39.3498 30.9595 35.0405C26.6502 30.7311 24.0307 25.0182 23.5775 18.9408C23.2543 14.6061 24.0483 10.2913 25.8447 6.395Z"
        />
      </clipPath>
      <linearGradient
        id="paint0_linear_3662_77383"
        x1="1"
        y1="1.00049"
        x2="64.9073"
        y2="61.0346"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0C0B22" stopOpacity="0.25" />
        <stop offset="1" stopColor="#0C0B22" stopOpacity="0.05" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_3662_77383"
        x1="1"
        y1="1.00049"
        x2="63.0312"
        y2="63.0923"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0C0B22" stopOpacity="0.25" />
        <stop offset="1" stopColor="#0C0B22" stopOpacity="0.02" />
      </linearGradient>
    </defs>
  </svg>
);
export default MoonIcon;
